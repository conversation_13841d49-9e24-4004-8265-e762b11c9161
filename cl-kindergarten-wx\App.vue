<script>

	// #ifdef APP-PLUS
	import appUpdate from '@/components/appUpdate/appUpdate.js'
	// #endif
	
	export default {
		onLaunch: function() {
		  //console.log('App Launch')
		   // #ifdef APP-PLUS
		  let systemInfo = uni.getSystemInfoSync()
		  const data={
		  	appid: systemInfo.appId,
		  	version: systemInfo.appVersion
		  }
		  console.log(data)
		  //把appid和版本发送请求服务器，看服务器上是否有更新	
		  this.$api.checkVersion(data).then(res => {
			  console.log(res)
		  	if(res.code==0){
				//如果返回0则表示有更新
		  		appUpdate(res)  
		  	}
		  })    
		  // #endif
		},
		onShow: function() {
			// #ifdef MP-WEIXIN
			// 获取小程序更新机制兼容
			const updateManager = wx.getUpdateManager()

			// 当向小程序后台请求完新版本信息，会进行回调。res: {hasUpdate: true, version: 1.0.0}
			updateManager.onCheckForUpdate(function (res) {
				if (res.hasUpdate) {					 // 有更新
					uni.showLoading({title:'更新中...'}); // 开始下载前，显示Loading
				}
			});
			// 当新版本下载完成，会进行回调
			updateManager.onUpdateReady(function () {
				uni.hideLoading();   // 关闭 Loading 
				uni.showModal({		// 弹确认框（强制更新）
					title:'更新提示',
					content:'更新完毕，是否重启？',
					success:function (res) {
						if (res.confirm) {
							updateManager.applyUpdate(); // 强制小程序重启并使用新版本。
						}
					}
				})
			});
			// 当新版本下载失败，会进行回调
			updateManager.onUpdateFailed(function () {
				uni.hideLoading();	// 关闭 Loading 
				uni.showToast({ title:'更新失败，稍后再试...', icon:"error" });
			});
			// #endif
		},
		onHide: function() { 
			
		}
	} 
</script>
<style lang="scss">
	@import "@/uni_modules/uview-ui/index.scss";
</style>
