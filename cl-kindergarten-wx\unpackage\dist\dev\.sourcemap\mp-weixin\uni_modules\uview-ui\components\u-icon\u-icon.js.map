{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-icon/u-icon.vue?3be5", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-icon/u-icon.vue?2c35", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-icon/u-icon.vue?f319", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-icon/u-icon.vue?ed8d", "uni-app:///uni_modules/uview-ui/components/u-icon/u-icon.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-icon/u-icon.vue?71e7", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-icon/u-icon.vue?43de"], "names": ["name", "props", "type", "default", "color", "size", "bold", "index", "hoverClass", "customPrefix", "label", "labelPos", "labelSize", "labelColor", "marginLeft", "marginTop", "marginRight", "marginBottom", "imgMode", "customStyle", "width", "height", "top", "showDecimalIcon", "inactiveColor", "percent", "computed", "customClass", "classes", "iconStyle", "style", "fontSize", "fontWeight", "isImg", "imgStyle", "decimalIconStyle", "decimalIconClass", "methods", "click", "touchstart"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuBpnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA,gBA+BA;EACAA;EACAC;IACA;IACAD;MACAE;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;EACA;EACAuB;IACAC;MACA;MACAC;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA;QACAA;MACA;MACA;MACA;;MAIA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;QACA;QACAV;MACA;MACA;MACA;QACAQ;MACA;MAEA;IACA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACA;MACAJ;MACAA;MACA;IACA;IACAK;MACA;MACAL;QACAC;QACAC;QACA;QACAV;QACAF;MACA;MACA;MACA;MACA;IACA;IACAgB;MACA;MACAR;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA,6GACAA;MACA;MACA;;MAIA;IACA;EACA;EACAS;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvQA;AAAA;AAAA;AAAA;AAAmqC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAvrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-icon/u-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-icon.vue?vue&type=template&id=2ee87dc9&scoped=true&\"\nvar renderjs\nimport script from \"./u-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./u-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-icon.vue?vue&type=style&index=0&id=2ee87dc9&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2ee87dc9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-icon/u-icon.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-icon.vue?vue&type=template&id=2ee87dc9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.customStyle])\n  var s1 = _vm.isImg ? _vm.__get_style([_vm.imgStyle]) : null\n  var s2 = !_vm.isImg ? _vm.__get_style([_vm.iconStyle]) : null\n  var s3 =\n    !_vm.isImg && _vm.showDecimalIcon\n      ? _vm.__get_style([_vm.decimalIconStyle])\n      : null\n  var g0 = _vm.label !== \"\" ? _vm.$u.addUnit(_vm.labelSize) : null\n  var g1 =\n    _vm.label !== \"\" && _vm.labelPos == \"right\"\n      ? _vm.$u.addUnit(_vm.marginLeft)\n      : null\n  var g2 =\n    _vm.label !== \"\" && _vm.labelPos == \"bottom\"\n      ? _vm.$u.addUnit(_vm.marginTop)\n      : null\n  var g3 =\n    _vm.label !== \"\" && _vm.labelPos == \"left\"\n      ? _vm.$u.addUnit(_vm.marginRight)\n      : null\n  var g4 =\n    _vm.label !== \"\" && _vm.labelPos == \"top\"\n      ? _vm.$u.addUnit(_vm.marginBottom)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-icon.vue?vue&type=script&lang=js&\"", "<template>\n\t<view :style=\"[customStyle]\" class=\"u-icon\" @tap=\"click\" :class=\"['u-icon--' + labelPos]\">\n\t\t<image class=\"u-icon__img\" v-if=\"isImg\" :src=\"name\" :mode=\"imgMode\" :style=\"[imgStyle]\"></image>\n\t\t<text v-else class=\"u-icon__icon\" :class=\"customClass\" :style=\"[iconStyle]\" :hover-class=\"hoverClass\"\n\t\t\t  @touchstart=\"touchstart\">\n\t\t\t<text v-if=\"showDecimalIcon\" :style=\"[decimalIconStyle]\" :class=\"decimalIconClass\" :hover-class=\"hoverClass\"\n\t\t\t\t  class=\"u-icon__decimal\">\n\t\t\t</text>\n\t\t</text>\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\n\t\t<text v-if=\"label !== ''\" class=\"u-icon__label\" :style=\"{\n\t\t\tcolor: labelColor,\n\t\t\tfontSize: $u.addUnit(labelSize),\n\t\t\tmarginLeft: labelPos == 'right' ? $u.addUnit(marginLeft) : 0,\n\t\t\tmarginTop: labelPos == 'bottom' ? $u.addUnit(marginTop) : 0,\n\t\t\tmarginRight: labelPos == 'left' ? $u.addUnit(marginRight) : 0,\n\t\t\tmarginBottom: labelPos == 'top' ? $u.addUnit(marginBottom) : 0,\n\t\t}\">{{ label }}\n\t\t</text>\n\t</view>\n</template>\n\n<script>\n/**\n * icon 图标\n * @description 基于字体的图标集，包含了大多数常见场景的图标。\n * @tutorial https://www.uviewui.com/components/icon.html\n * @property {String} name 图标名称，见示例图标集\n * @property {String} color 图标颜色（默认inherit）\n * @property {String | Number} size 图标字体大小，单位rpx（默认32）\n * @property {String | Number} label-size label字体大小，单位rpx（默认28）\n * @property {String} label 图标右侧的label文字（默认28）\n * @property {String} label-pos label文字相对于图标的位置，只能right或bottom（默认right）\n * @property {String} label-color label字体颜色（默认#606266）\n * @property {Object} custom-style icon的样式，对象形式\n * @property {String} custom-prefix 自定义字体图标库时，需要写上此值\n * @property {String | Number} margin-left label在右侧时与图标的距离，单位rpx（默认6）\n * @property {String | Number} margin-top label在下方时与图标的距离，单位rpx（默认6）\n * @property {String | Number} margin-bottom label在上方时与图标的距离，单位rpx（默认6）\n * @property {String | Number} margin-right label在左侧时与图标的距离，单位rpx（默认6）\n * @property {String} label-pos label相对于图标的位置，只能right或bottom（默认right）\n * @property {String} index 一个用于区分多个图标的值，点击图标时通过click事件传出\n * @property {String} hover-class 图标按下去的样式类，用法同uni的view组件的hover-class参数，详情见官网\n * @property {String} width 显示图片小图标时的宽度\n * @property {String} height 显示图片小图标时的高度\n * @property {String} top 图标在垂直方向上的定位\n * @property {String} top 图标在垂直方向上的定位\n * @property {String} top 图标在垂直方向上的定位\n * @property {Boolean} show-decimal-icon 是否为DecimalIcon\n * @property {String} inactive-color 背景颜色，可接受主题色，仅Decimal时有效\n * @property {String | Number} percent 显示的百分比，仅Decimal时有效\n * @event {Function} click 点击图标时触发\n * @example <u-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></u-icon>\n */\nexport default {\n\tname: 'u-icon',\n\tprops: {\n\t\t// 图标类名\n\t\tname: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 图标颜色，可接受主题色\n\t\tcolor: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 字体大小，单位rpx\n\t\tsize: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 'inherit'\n\t\t},\n\t\t// 是否显示粗体\n\t\tbold: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 点击图标的时候传递事件出去的index（用于区分点击了哪一个）\n\t\tindex: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: ''\n\t\t},\n\t\t// 触摸图标时的类名\n\t\thoverClass: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 自定义扩展前缀，方便用户扩展自己的图标库\n\t\tcustomPrefix: {\n\t\t\ttype: String,\n\t\t\tdefault: 'uicon'\n\t\t},\n\t\t// 图标右边或者下面的文字\n\t\tlabel: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: ''\n\t\t},\n\t\t// label的位置，只能右边或者下边\n\t\tlabelPos: {\n\t\t\ttype: String,\n\t\t\tdefault: 'right'\n\t\t},\n\t\t// label的大小\n\t\tlabelSize: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '28'\n\t\t},\n\t\t// label的颜色\n\t\tlabelColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#606266'\n\t\t},\n\t\t// label与图标的距离(横向排列)\n\t\tmarginLeft: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '6'\n\t\t},\n\t\t// label与图标的距离(竖向排列)\n\t\tmarginTop: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '6'\n\t\t},\n\t\t// label与图标的距离(竖向排列)\n\t\tmarginRight: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '6'\n\t\t},\n\t\t// label与图标的距离(竖向排列)\n\t\tmarginBottom: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '6'\n\t\t},\n\t\t// 图片的mode\n\t\timgMode: {\n\t\t\ttype: String,\n\t\t\tdefault: 'widthFix'\n\t\t},\n\t\t// 自定义样式\n\t\tcustomStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t\t// 用于显示图片小图标时，图片的宽度\n\t\twidth: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: ''\n\t\t},\n\t\t// 用于显示图片小图标时，图片的高度\n\t\theight: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: ''\n\t\t},\n\t\t// 用于解决某些情况下，让图标垂直居中的用途\n\t\ttop: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 0\n\t\t},\n\t\t// 是否为DecimalIcon\n\t\tshowDecimalIcon: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 背景颜色，可接受主题色，仅Decimal时有效\n\t\tinactiveColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#ececec'\n\t\t},\n\t\t// 显示的百分比，仅Decimal时有效\n\t\tpercent: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: '50'\n\t\t}\n\t},\n\tcomputed: {\n\t\tcustomClass() {\n\t\t\tlet classes = []\n\t\t\tclasses.push(this.customPrefix + '-' + this.name)\n\t\t\t// uView的自定义图标类名为u-iconfont\n\t\t\tif (this.customPrefix == 'uicon') {\n\t\t\t\tclasses.push('u-iconfont')\n\t\t\t} else {\n\t\t\t\tclasses.push(this.customPrefix)\n\t\t\t}\n\t\t\t// 主题色，通过类配置\n\t\t\tif (this.showDecimalIcon && this.inactiveColor && this.$u.config.type.includes(this.inactiveColor)) {\n\t\t\t\tclasses.push('u-icon__icon--' + this.inactiveColor)\n\t\t\t} else if (this.color && this.$u.config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\n\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\n\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\n\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\n\t\t\tclasses = classes.join(' ')\n\t\t\t//#endif\n\t\t\treturn classes\n\t\t},\n\t\ticonStyle() {\n\t\t\tlet style = {}\n\t\t\tstyle = {\n\t\t\t\tfontSize: this.size == 'inherit' ? 'inherit' : this.$u.addUnit(this.size),\n\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\n\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\n\t\t\t\ttop: this.$u.addUnit(this.top)\n\t\t\t}\n\t\t\t// 非主题色值时，才当作颜色值\n\t\t\tif (this.showDecimalIcon && this.inactiveColor && !this.$u.config.type.includes(this.inactiveColor)) {\n\t\t\t\tstyle.color = this.inactiveColor\n\t\t\t} else if (this.color && !this.$u.config.type.includes(this.color)) style.color = this.color\n\n\t\t\treturn style\n\t\t},\n\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\tisImg() {\n\t\t\treturn this.name.indexOf('/') !== -1\n\t\t},\n\t\timgStyle() {\n\t\t\tlet style = {}\n\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\n\t\t\tstyle.width = this.width ? this.$u.addUnit(this.width) : this.$u.addUnit(this.size)\n\t\t\tstyle.height = this.height ? this.$u.addUnit(this.height) : this.$u.addUnit(this.size)\n\t\t\treturn style\n\t\t},\n\t\tdecimalIconStyle() {\n\t\t\tlet style = {}\n\t\t\tstyle = {\n\t\t\t\tfontSize: this.size == 'inherit' ? 'inherit' : this.$u.addUnit(this.size),\n\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\n\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\n\t\t\t\ttop: this.$u.addUnit(this.top),\n\t\t\t\twidth: this.percent + '%'\n\t\t\t}\n\t\t\t// 非主题色值时，才当作颜色值\n\t\t\tif (this.color && !this.$u.config.type.includes(this.color)) style.color = this.color\n\t\t\treturn style\n\t\t},\n\t\tdecimalIconClass() {\n\t\t\tlet classes = []\n\t\t\tclasses.push(this.customPrefix + '-' + this.name)\n\t\t\t// uView的自定义图标类名为u-iconfont\n\t\t\tif (this.customPrefix == 'uicon') {\n\t\t\t\tclasses.push('u-iconfont')\n\t\t\t} else {\n\t\t\t\tclasses.push(this.customPrefix)\n\t\t\t}\n\t\t\t// 主题色，通过类配置\n\t\t\tif (this.color && this.$u.config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\n\t\t\telse classes.push('u-icon__icon--primary')\n\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\n\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\n\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\n\t\t\tclasses = classes.join(' ')\n\t\t\t//#endif\n\t\t\treturn classes\n\t\t}\n\t},\n\tmethods: {\n\t\tclick() {\n\t\t\tthis.$emit('click', this.index)\n\t\t},\n\t\ttouchstart() {\n\t\t\tthis.$emit('touchstart', this.index)\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../libs/css/style.components.scss\";\n@import '../../iconfont.css';\n\n.u-icon {\n\tdisplay: inline-flex;\n\talign-items: center;\n\n\t&--left {\n\t\tflex-direction: row-reverse;\n\t\talign-items: center;\n\t}\n\n\t&--right {\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t&--top {\n\t\tflex-direction: column-reverse;\n\t\tjustify-content: center;\n\t}\n\n\t&--bottom {\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t}\n\n\t&__icon {\n\t\tposition: relative;\n\n\t\t&--primary {\n\t\t\tcolor: $u-type-primary;\n\t\t}\n\n\t\t&--success {\n\t\t\tcolor: $u-type-success;\n\t\t}\n\n\t\t&--error {\n\t\t\tcolor: $u-type-error;\n\t\t}\n\n\t\t&--warning {\n\t\t\tcolor: $u-type-warning;\n\t\t}\n\n\t\t&--info {\n\t\t\tcolor: $u-type-info;\n\t\t}\n\t}\n\n\t&__decimal {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tdisplay: inline-block;\n\t\toverflow: hidden;\n\t}\n\n\t&__img {\n\t\theight: auto;\n\t\twill-change: transform;\n\t}\n\n\t&__label {\n\t\tline-height: 1;\n\t}\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-icon.vue?vue&type=style&index=0&id=2ee87dc9&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-icon.vue?vue&type=style&index=0&id=2ee87dc9&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753698996066\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}