{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?0029", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?b2f1", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?efa4", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?013b", "uni-app:///uni_modules/uview-ui/components/u-avatar/u-avatar.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?d965", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?b08f"], "names": ["name", "props", "bgColor", "type", "default", "src", "size", "mode", "text", "imgMode", "index", "sexIcon", "levelIcon", "levelBgColor", "sexBgColor", "showSex", "showLevel", "data", "error", "avatar", "watch", "computed", "wrapStyle", "style", "imgStyle", "uText", "uSexStyle", "uLevelStyle", "methods", "loadError", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwBtnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAmBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAf;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAgB;IACAC;MACA;MACAC,0EACA;MACAA;MACAA;MACAA;MACAA;MACA;MACA;IACA;IACAC;MACA;MACAD;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClLA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-avatar/u-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-avatar.vue?vue&type=template&id=d3651d6e&scoped=true&\"\nvar renderjs\nimport script from \"./u-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-avatar.vue?vue&type=style&index=0&id=d3651d6e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d3651d6e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-avatar/u-avatar.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-avatar.vue?vue&type=template&id=d3651d6e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapStyle])\n  var s1 = !_vm.uText && _vm.avatar ? _vm.__get_style([_vm.imgStyle]) : null\n  var s2 = _vm.showSex ? _vm.__get_style([_vm.uSexStyle]) : null\n  var s3 = _vm.showLevel ? _vm.__get_style([_vm.uLevelStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-avatar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-avatar\" :style=\"[wrapStyle]\" @tap=\"click\">\n\t\t<image\n\t\t\t@error=\"loadError\"\n\t\t\t:style=\"[imgStyle]\"\n\t\t\tclass=\"u-avatar__img\"\n\t\t\tv-if=\"!uText && avatar\"\n\t\t\t:src=\"avatar\" \n\t\t\t:mode=\"imgMode\"\n\t\t></image>\n\t\t<text class=\"u-line-1\" v-else-if=\"uText\" :style=\"{\n\t\t\tfontSize: '38rpx'\n\t\t}\">{{uText}}</text>\n\t\t<slot v-else></slot>\n\t\t<view class=\"u-avatar__sex\" v-if=\"showSex\" :class=\"['u-avatar__sex--' + sexIcon]\" :style=\"[uSexStyle]\">\n\t\t\t<u-icon :name=\"sexIcon\" size=\"20\"></u-icon>\n\t\t</view>\n\t\t<view class=\"u-avatar__level\" v-if=\"showLevel\" :style=\"[uLevelStyle]\">\n\t\t\t<u-icon :name=\"levelIcon\" size=\"20\"></u-icon>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tlet base64Avatar = \"data:image/jpg;base64,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\";\n\t/**\n\t * avatar 头像\n\t * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\n\t * @tutorial https://www.uviewui.com/components/avatar.html\n\t * @property {String} bg-color 背景颜色，一般显示文字时用（默认#ffffff）\n\t * @property {String} src 头像路径，如加载失败，将会显示默认头像\n\t * @property {String Number} size 头像尺寸，可以为指定字符串(large, default, mini)，或者数值，单位rpx（默认default）\n\t * @property {String} mode 显示类型，见上方说明（默认circle）\n\t * @property {String} sex-icon 性别图标，man-男，woman-女（默认man）\n\t * @property {String} level-icon 等级图标（默认level）\n\t * @property {String} sex-bg-color 性别图标背景颜色\n\t * @property {String} level-bg-color 等级图标背景颜色\n\t * @property {String} show-sex 是否显示性别图标（默认false）\n\t * @property {String} show-level 是否显示等级图标（默认false）\n\t * @property {String} img-mode 头像图片的裁剪类型，与uni的image组件的mode参数一致，如效果达不到需求，可尝试传widthFix值（默认aspectFill）\n\t * @property {String} index 用户传递的标识符值，如果是列表循环，可穿v-for的index值\n\t * @event {Function} click 头像被点击\n\t * @example <u-avatar :src=\"src\"></u-avatar>\n\t */\n\texport default {\n\t\tname: 'u-avatar',\n\t\tprops: {\n\t\t\t// 背景颜色\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'transparent'\n\t\t\t},\n\t\t\t// 头像路径\n\t\t\tsrc: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 尺寸，large-大，default-中等，mini-小，如果为数值，则单位为rpx\n\t\t\t// 宽度等于高度\n\t\t\tsize: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 'default'\n\t\t\t},\n\t\t\t// 头像模型，square-带圆角方形，circle-圆形\n\t\t\tmode: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'circle'\n\t\t\t},\n\t\t\t// 文字内容\n\t\t\ttext: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 图片的裁剪模型\n\t\t\timgMode: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'aspectFill'\n\t\t\t},\n\t\t\t// 标识符\n\t\t\tindex: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 右上角性别角标，man-男，woman-女\n\t\t\tsexIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'man'\n\t\t\t},\n\t\t\t// 右下角的等级图标\n\t\t\tlevelIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'level'\n\t\t\t},\n\t\t\t// 右下角等级图标背景颜色\n\t\t\tlevelBgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 右上角性别图标的背景颜色\n\t\t\tsexBgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 是否显示性别图标\n\t\t\tshowSex: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 是否显示等级图标\n\t\t\tshowLevel: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\terror: false,\n\t\t\t\t// 头像的地址，因为如果加载错误，需要赋值为默认图片，props值无法修改，所以需要一个中间值\n                avatar: this.src ? this.src : base64Avatar, \n\t\t\t}\n\t\t},\n        watch: {\n            src(n) {\n                // 用户可能会在头像加载失败时，再次修改头像值，所以需要重新赋值\n                if(!n) {\n\t\t\t\t\t// 如果传入null或者''，或者undefined，显示默认头像\n\t\t\t\t\tthis.avatar = base64Avatar;\n\t\t\t\t\tthis.error = true;\n\t\t\t\t} else {\n\t\t\t\t\tthis.avatar = n;\n\t\t\t\t\tthis.error = false;\n\t\t\t\t}\n            }\n        },\n\t\tcomputed: {\n\t\t\twrapStyle() {\n\t\t\t\tlet style = {};  \n\t\t\t\tstyle.height = this.size == 'large' ? '120rpx' : this.size == 'default' ?\n\t\t\t\t'90rpx' : this.size == 'mini' ? '70rpx' : this.size + 'rpx';\n\t\t\t\tstyle.width = style.height;\n\t\t\t\tstyle.flex = `0 0 ${style.height}`;\n\t\t\t\tstyle.backgroundColor = this.bgColor;\n\t\t\t\tstyle.borderRadius = this.mode == 'circle' ? '500px' : '5px';\n\t\t\t\tif(this.text) style.padding = `0 6rpx`;\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\timgStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tstyle.borderRadius = this.mode == 'circle' ? '500px' : '5px';\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t// 取字符串的第一个字符\n\t\t\tuText() {\n\t\t\t\treturn String(this.text)[0];\n\t\t\t},\n\t\t\t// 性别图标的自定义样式\n\t\t\tuSexStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tif(this.sexBgColor) style.backgroundColor = this.sexBgColor;\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t// 等级图标的自定义样式\n\t\t\tuLevelStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tif(this.levelBgColor) style.backgroundColor = this.levelBgColor;\n\t\t\t\treturn style;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 图片加载错误时，显示默认头像\n\t\t\tloadError() {\n\t\t\t\tthis.error = true;\n                this.avatar = base64Avatar;\n\t\t\t},\n\t\t\tclick() {\n\t\t\t\tthis.$emit('click', this.index);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/style.components.scss\";\n\n\t.u-avatar {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: inline-flex;\t\t\n\t\t/* #endif */\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: $u-content-color;\n\t\tborder-radius: 10px;\n\t\tposition: relative;\n\t\t\n\t\t&__img {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t}\n\t\t\n\t\t&__sex {\n\t\t\tposition: absolute;\n\t\t\twidth: 32rpx;\n\t\t\tcolor: #ffffff;\n\t\t\theight: 32rpx;\n\t\t\t@include vue-flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tborder-radius: 100rpx;\n\t\t\ttop: 5%;\n\t\t\tz-index: 1;\n\t\t\tright: -7%;\n\t\t\tborder: 1px #ffffff solid;\n\t\t\t\n\t\t\t&--man {\n\t\t\t\tbackground-color: $u-type-primary;\n\t\t\t}\n\t\t\t\n\t\t\t&--woman {\n\t\t\t\tbackground-color: $u-type-error;\n\t\t\t}\n\t\t\t\n\t\t\t&--none {\n\t\t\t\tbackground-color: $u-type-warning;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&__level {\n\t\t\tposition: absolute;\n\t\t\twidth: 32rpx;\n\t\t\tcolor: #ffffff;\n\t\t\theight: 32rpx;\n\t\t\t@include vue-flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tborder-radius: 100rpx;\n\t\t\tbottom: 5%; \n\t\t\tz-index: 1;\n\t\t\tright: -7%;\n\t\t\tborder: 1px #ffffff solid;\n\t\t\tbackground-color: $u-type-warning;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-avatar.vue?vue&type=style&index=0&id=d3651d6e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-avatar.vue?vue&type=style&index=0&id=d3651d6e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753698995872\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}