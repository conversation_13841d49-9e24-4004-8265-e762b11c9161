{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue?0ed7", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue?b323", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue?e464", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue?231f", "uni-app:///uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue?818b", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue?42ae"], "names": ["name", "props", "seconds", "type", "default", "startText", "changeText", "endText", "keepRunning", "<PERSON><PERSON><PERSON>", "data", "secNum", "timer", "canGetCode", "mounted", "watch", "immediate", "handler", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>ning", "uni", "start", "clearInterval", "reset", "changeEvent", "setTimeToStorage", "key", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6mB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACOjoB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAb;MACAc;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAC;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;UACAA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAD;MACA;MACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAL;UACAM;UACAhB;QACA;MACA;IACA;EACA;EACA;EACAiB;IACA;IACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvJA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,spCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-verification-code/u-verification-code.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-verification-code.vue?vue&type=template&id=2356090e&scoped=true&\"\nvar renderjs\nimport script from \"./u-verification-code.vue?vue&type=script&lang=js&\"\nexport * from \"./u-verification-code.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-verification-code.vue?vue&type=style&index=0&id=2356090e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2356090e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-verification-code.vue?vue&type=template&id=2356090e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-verification-code.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-verification-code.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-code-wrap\">\n\t\t<!-- 此组件功能由js完成，无需写html逻辑 -->\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * verificationCode 验证码输入框\n\t * @description 考虑到用户实际发送验证码的场景，可能是一个按钮，也可能是一段文字，提示语各有不同，所以本组件 不提供界面显示，只提供提示语，由用户将提示语嵌入到具体的场景\n\t * @tutorial https://www.uviewui.com/components/verificationCode.html\n\t * @property {Number String} seconds 倒计时所需的秒数（默认60）\n\t * @property {String} start-text 开始前的提示语，见官网说明（默认获取验证码）\n\t * @property {String} change-text 倒计时期间的提示语，必须带有字母\"x\"，见官网说明（默认X秒重新获取）\n\t * @property {String} end-text 倒计结束的提示语，见官网说明（默认重新获取）\n\t * @property {Boolean} keep-running 是否在H5刷新或各端返回再进入时继续倒计时（默认false）\n\t * @event {Function} change 倒计时期间，每秒触发一次\n\t * @event {Function} start 开始倒计时触发\n\t * @event {Function} end 结束倒计时触发\n\t * @example <u-verification-code :seconds=\"seconds\" @end=\"end\" @start=\"start\" ref=\"uCode\" \n\t */\n\texport default {\n\t\tname: \"u-verification-code\",\n\t\tprops: {\n\t\t\t// 倒计时总秒数\n\t\t\tseconds: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 60\n\t\t\t},\n\t\t\t// 尚未开始时提示\n\t\t\tstartText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '获取验证码'\n\t\t\t},\n\t\t\t// 正在倒计时中的提示\n\t\t\tchangeText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'X秒重新获取'\n\t\t\t},\n\t\t\t// 倒计时结束时的提示\n\t\t\tendText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '重新获取'\n\t\t\t},\n\t\t\t// 是否在H5刷新或各端返回再进入时继续倒计时\n\t\t\tkeepRunning: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 为了区分多个页面，或者一个页面多个倒计时组件本地存储的继续倒计时变了\n\t\t\tuniqueKey: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsecNum: this.seconds,\n\t\t\t\ttimer: null,\n\t\t\t\tcanGetCode: true, // 是否可以执行验证码操作\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.checkKeepRunning();\n\t\t},\n\t\twatch: {\n\t\t\tseconds: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tthis.secNum = n;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tcheckKeepRunning() {\n\t\t\t\t// 获取上一次退出页面(H5还包括刷新)时的时间戳，如果没有上次的保存，此值可能为空\n\t\t\t\tlet lastTimestamp = Number(uni.getStorageSync(this.uniqueKey + '_$uCountDownTimestamp'));\n\t\t\t\tif(!lastTimestamp) return this.changeEvent(this.startText);\n\t\t\t\t// 当前秒的时间戳\n\t\t\t\tlet nowTimestamp = Math.floor((+ new Date()) / 1000);\n\t\t\t\t// 判断当前的时间戳，是否小于上一次的本该按设定结束，却提前结束的时间戳\n\t\t\t\tif(this.keepRunning && lastTimestamp && lastTimestamp > nowTimestamp) {\n\t\t\t\t\t// 剩余尚未执行完的倒计秒数\n\t\t\t\t\tthis.secNum = lastTimestamp - nowTimestamp;\n\t\t\t\t\t// 清除本地保存的变量\n\t\t\t\t\tuni.removeStorageSync(this.uniqueKey + '_$uCountDownTimestamp');\n\t\t\t\t\t// 开始倒计时\n\t\t\t\t\tthis.start();\n\t\t\t\t} else {\n\t\t\t\t\t// 如果不存在需要继续上一次的倒计时，执行正常的逻辑\n\t\t\t\t\tthis.changeEvent(this.startText);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 开始倒计时\n\t\t\tstart() {\n\t\t\t\t// 防止快速点击获取验证码的按钮而导致内部产生多个定时器导致混乱\n\t\t\t\tif(this.timer) {\n\t\t\t\t\tclearInterval(this.timer);\n\t\t\t\t\tthis.timer = null;\n\t\t\t\t}\n\t\t\t\tthis.$emit('start');\n\t\t\t\tthis.canGetCode = false;\n\t\t\t\t// 这里放这句，是为了一开始时就提示，否则要等setInterval的1秒后才会有提示\n\t\t\t\tthis.changeEvent(this.changeText.replace(/x|X/, this.secNum));\n\t\t\t\tthis.setTimeToStorage();\n\t\t\t\tthis.timer = setInterval(() => {\n\t\t\t\t\tif (--this.secNum) {\n\t\t\t\t\t\t// 用当前倒计时的秒数替换提示字符串中的\"x\"字母\n\t\t\t\t\t\tthis.changeEvent(this.changeText.replace(/x|X/, this.secNum));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tclearInterval(this.timer);\n\t\t\t\t\t\tthis.timer = null;\n\t\t\t\t\t\tthis.changeEvent(this.endText);\n\t\t\t\t\t\tthis.secNum = this.seconds;\n\t\t\t\t\t\tthis.$emit('end');\n\t\t\t\t\t\tthis.canGetCode = true;\n\t\t\t\t\t}\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\t// 重置，可以让用户再次获取验证码\n\t\t\treset() {\n\t\t\t\tthis.canGetCode = true;\n\t\t\t\tclearInterval(this.timer);\n\t\t\t\tthis.secNum = this.seconds;\n\t\t\t\tthis.changeEvent(this.endText);\n\t\t\t},\n\t\t\tchangeEvent(text) {\n\t\t\t\tthis.$emit('change', text);\n\t\t\t},\n\t\t\t// 保存时间戳，为了防止倒计时尚未结束，H5刷新或者各端的右上角返回上一页再进来\n\t\t\tsetTimeToStorage() {\n\t\t\t\tif(!this.keepRunning || !this.timer) return;\n\t\t\t\t// 记录当前的时间戳，为了下次进入页面，如果还在倒计时内的话，继续倒计时\n\t\t\t\t// 倒计时尚未结束，结果大于0；倒计时已经开始，就会小于初始值，如果等于初始值，说明没有开始倒计时，无需处理\n\t\t\t\tif(this.secNum > 0 && this.secNum <= this.seconds) {\n\t\t\t\t\t// 获取当前时间戳(+ new Date()为特殊写法)，除以1000变成秒，再去除小数部分\n\t\t\t\t\tlet nowTimestamp = Math.floor((+ new Date()) / 1000);\n\t\t\t\t\t// 将本该结束时候的时间戳保存起来 => 当前时间戳 + 剩余的秒数\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.uniqueKey + '_$uCountDownTimestamp',\n\t\t\t\t\t\tdata: nowTimestamp + Number(this.secNum)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 组件销毁的时候，清除定时器，否则定时器会继续存在，系统不会自动清除\n\t\tbeforeDestroy() {\n\t\t\tthis.setTimeToStorage();\n\t\t\tclearTimeout(this.timer);\n\t\t\tthis.timer = null;\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/style.components.scss\";\n\t\n\t.u-code-wrap {\n\t\twidth: 0;\n\t\theight: 0;\n\t\tposition: fixed;\n\t\tz-index: -1;\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-verification-code.vue?vue&type=style&index=0&id=2356090e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-verification-code.vue?vue&type=style&index=0&id=2356090e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753698995742\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}