{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/mp-html.vue?68f2", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/mp-html.vue?ad52", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/mp-html.vue?fe48", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/mp-html.vue?a7c9", "uni-app:///uni_modules/mp-html/components/mp-html/mp-html.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/mp-html.vue?e7dc", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/mp-html.vue?e7f7"], "names": ["name", "data", "nodes", "props", "containerStyle", "type", "default", "content", "copyLink", "domain", "errorImg", "lazyLoad", "loadingImg", "pauseVideo", "previewImg", "scrollTable", "selectable", "setTitle", "showImgMenu", "tagStyle", "useAnchor", "components", "node", "watch", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "in", "page", "selector", "scrollTop", "navigateTo", "reject", "offset", "deep", "select", "uni", "duration", "resolve", "getText", "text", "traversal", "getRect", "pauseMedia", "setPlaybackRate", "<PERSON><PERSON><PERSON><PERSON>", "height", "setTimeout", "_hook"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAimB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0CrnB;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eAIA;EACAA;EACAC;IACA;MACAC;IAIA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;IACAC;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;IACAC;IACAC;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;IACAC;EACA;EAKAC;IACAC;EACA;EAEAC;IACAhB;MACA;IACA;EACA;EACAiB;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAEA;QACA;UACAC;UACAC;UACAC;QACA;MACA;IAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAC;UACA;QACA;QACAC;QAiBA;QAEAC;QAEA,yCAEAP,uCAEAQ;QACA;UACAN,mDACAM;QACA;UACA;UACAN;QACA;;QACAA;UACA;YACAG;YACA;UACA;UACA;UACA;YACA;YACA;UACA;YACA;YACAI;cACAN;cACAO;YACA;UACA;UACAC;QACA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;UACA;YACAC;UACA;YACAA;UACA;YACA;YACA;YACA;cACAA;YACA;YACA;YACA;cACAC;YACA;YACA;cACAD;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAE;MAAA;MACA;QACAN,0BAEAT,WAEAQ;UAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAQ;MACA;QACA;MACA;IAYA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;IAYA;IAEA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MACA;MAMA;MAGA;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;YACAC;YACAC;cACA;YACA;UACA;QACA;QACA;MACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IAEA;IAEA;AACA;AACA;IACAC;MACA;QACA;UACA;QACA;MACA;IACA;EA6GA;AACA;AAAA,2B;;;;;;;;;;;;;AC/dA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,o3BAAG,EAAC,C;;;;;;;;;;;ACAv4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/mp-html/components/mp-html/mp-html.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mp-html.vue?vue&type=template&id=0cfd6ca1&\"\nvar renderjs\nimport script from \"./mp-html.vue?vue&type=script&lang=js&\"\nexport * from \"./mp-html.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mp-html.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/mp-html/components/mp-html/mp-html.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mp-html.vue?vue&type=template&id=0cfd6ca1&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mp-html.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mp-html.vue?vue&type=script&lang=js&\"", "<template>\n  <view id=\"_root\" :class=\"(selectable?'_select ':'')+'_root'\" :style=\"containerStyle\">\n    <slot v-if=\"!nodes[0]\" />\n    <!-- #ifndef APP-PLUS-NVUE -->\n    <node v-else :childs=\"nodes\" :opts=\"[lazyLoad,loadingImg,errorImg,showImgMenu,selectable]\" name=\"span\" />\n    <!-- #endif -->\n    <!-- #ifdef APP-PLUS-NVUE -->\n    <web-view ref=\"web\" src=\"/uni_modules/mp-html/static/app-plus/mp-html/local.html\" :style=\"'margin-top:-2px;height:' + height + 'px'\" @onPostMessage=\"_onMessage\" />\n    <!-- #endif -->\n  </view>\n</template>\n\n<script>\n/**\n * mp-html v2.4.2\n * @description 富文本组件\n * @tutorial https://github.com/jin-yufeng/mp-html\n * @property {String} container-style 容器的样式\n * @property {String} content 用于渲染的 html 字符串\n * @property {Boolean} copy-link 是否允许外部链接被点击时自动复制\n * @property {String} domain 主域名，用于拼接链接\n * @property {String} error-img 图片出错时的占位图链接\n * @property {Boolean} lazy-load 是否开启图片懒加载\n * @property {string} loading-img 图片加载过程中的占位图链接\n * @property {Boolean} pause-video 是否在播放一个视频时自动暂停其他视频\n * @property {Boolean} preview-img 是否允许图片被点击时自动预览\n * @property {Boolean} scroll-table 是否给每个表格添加一个滚动层使其能单独横向滚动\n * @property {Boolean | String} selectable 是否开启长按复制\n * @property {Boolean} set-title 是否将 title 标签的内容设置到页面标题\n * @property {Boolean} show-img-menu 是否允许图片被长按时显示菜单\n * @property {Object} tag-style 标签的默认样式\n * @property {Boolean | Number} use-anchor 是否使用锚点链接\n * @event {Function} load dom 结构加载完毕时触发\n * @event {Function} ready 所有图片加载完毕时触发\n * @event {Function} imgtap 图片被点击时触发\n * @event {Function} linktap 链接被点击时触发\n * @event {Function} play 音视频播放时触发\n * @event {Function} error 媒体加载出错时触发\n */\n// #ifndef APP-PLUS-NVUE\nimport node from './node/node'\n// #endif\nimport Parser from './parser'\nconst plugins=[]\n// #ifdef APP-PLUS-NVUE\nconst dom = weex.requireModule('dom')\n// #endif\nexport default {\n  name: 'mp-html',\n  data () {\n    return {\n      nodes: [],\n      // #ifdef APP-PLUS-NVUE\n      height: 3\n      // #endif\n    }\n  },\n  props: {\n    containerStyle: {\n      type: String,\n      default: ''\n    },\n    content: {\n      type: String,\n      default: ''\n    },\n    copyLink: {\n      type: [Boolean, String],\n      default: true\n    },\n    domain: String,\n    errorImg: {\n      type: String,\n      default: ''\n    },\n    lazyLoad: {\n      type: [Boolean, String],\n      default: false\n    },\n    loadingImg: {\n      type: String,\n      default: ''\n    },\n    pauseVideo: {\n      type: [Boolean, String],\n      default: true\n    },\n    previewImg: {\n      type: [Boolean, String],\n      default: true\n    },\n    scrollTable: [Boolean, String],\n    selectable: [Boolean, String],\n    setTitle: {\n      type: [Boolean, String],\n      default: true\n    },\n    showImgMenu: {\n      type: [Boolean, String],\n      default: true\n    },\n    tagStyle: Object,\n    useAnchor: [Boolean, Number]\n  },\n  // #ifdef VUE3\n  emits: ['load', 'ready', 'imgtap', 'linktap', 'play', 'error'],\n  // #endif\n  // #ifndef APP-PLUS-NVUE\n  components: {\n    node\n  },\n  // #endif\n  watch: {\n    content (content) {\n      this.setContent(content)\n    }\n  },\n  created () {\n    this.plugins = []\n    for (let i = plugins.length; i--;) {\n      this.plugins.push(new plugins[i](this))\n    }\n  },\n  mounted () {\n    if (this.content && !this.nodes.length) {\n      this.setContent(this.content)\n    }\n  },\n  beforeDestroy () {\n    this._hook('onDetached')\n  },\n  methods: {\n    /**\n     * @description 将锚点跳转的范围限定在一个 scroll-view 内\n     * @param {Object} page scroll-view 所在页面的示例\n     * @param {String} selector scroll-view 的选择器\n     * @param {String} scrollTop scroll-view scroll-top 属性绑定的变量名\n     */\n    in (page, selector, scrollTop) {\n      // #ifndef APP-PLUS-NVUE\n      if (page && selector && scrollTop) {\n        this._in = {\n          page,\n          selector,\n          scrollTop\n        }\n      }\n      // #endif\n    },\n\n    /**\n     * @description 锚点跳转\n     * @param {String} id 要跳转的锚点 id\n     * @param {Number} offset 跳转位置的偏移量\n     * @returns {Promise}\n     */\n    navigateTo (id, offset) {\n      return new Promise((resolve, reject) => {\n        if (!this.useAnchor) {\n          reject(Error('Anchor is disabled'))\n          return\n        }\n        offset = offset || parseInt(this.useAnchor) || 0\n        // #ifdef APP-PLUS-NVUE\n        if (!id) {\n          dom.scrollToElement(this.$refs.web, {\n            offset\n          })\n          resolve()\n        } else {\n          this._navigateTo = {\n            resolve,\n            reject,\n            offset\n          }\n          this.$refs.web.evalJs('uni.postMessage({data:{action:\"getOffset\",offset:(document.getElementById(' + id + ')||{}).offsetTop}})')\n        }\n        // #endif\n        // #ifndef APP-PLUS-NVUE\n        let deep = ' '\n        // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\n        deep = '>>>'\n        // #endif\n        const selector = uni.createSelectorQuery()\n          // #ifndef MP-ALIPAY\n          .in(this._in ? this._in.page : this)\n          // #endif\n          .select((this._in ? this._in.selector : '._root') + (id ? `${deep}#${id}` : '')).boundingClientRect()\n        if (this._in) {\n          selector.select(this._in.selector).scrollOffset()\n            .select(this._in.selector).boundingClientRect()\n        } else {\n          // 获取 scroll-view 的位置和滚动距离\n          selector.selectViewport().scrollOffset() // 获取窗口的滚动距离\n        }\n        selector.exec(res => {\n          if (!res[0]) {\n            reject(Error('Label not found'))\n            return\n          }\n          const scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + offset\n          if (this._in) {\n            // scroll-view 跳转\n            this._in.page[this._in.scrollTop] = scrollTop\n          } else {\n            // 页面跳转\n            uni.pageScrollTo({\n              scrollTop,\n              duration: 300\n            })\n          }\n          resolve()\n        })\n        // #endif\n      })\n    },\n\n    /**\n     * @description 获取文本内容\n     * @return {String}\n     */\n    getText (nodes) {\n      let text = '';\n      (function traversal (nodes) {\n        for (let i = 0; i < nodes.length; i++) {\n          const node = nodes[i]\n          if (node.type === 'text') {\n            text += node.text.replace(/&amp;/g, '&')\n          } else if (node.name === 'br') {\n            text += '\\n'\n          } else {\n            // 块级标签前后加换行\n            const isBlock = node.name === 'p' || node.name === 'div' || node.name === 'tr' || node.name === 'li' || (node.name[0] === 'h' && node.name[1] > '0' && node.name[1] < '7')\n            if (isBlock && text && text[text.length - 1] !== '\\n') {\n              text += '\\n'\n            }\n            // 递归获取子节点的文本\n            if (node.children) {\n              traversal(node.children)\n            }\n            if (isBlock && text[text.length - 1] !== '\\n') {\n              text += '\\n'\n            } else if (node.name === 'td' || node.name === 'th') {\n              text += '\\t'\n            }\n          }\n        }\n      })(nodes || this.nodes)\n      return text\n    },\n\n    /**\n     * @description 获取内容大小和位置\n     * @return {Promise}\n     */\n    getRect () {\n      return new Promise((resolve, reject) => {\n        uni.createSelectorQuery()\n          // #ifndef MP-ALIPAY\n          .in(this)\n          // #endif\n          .select('#_root').boundingClientRect().exec(res => res[0] ? resolve(res[0]) : reject(Error('Root label not found')))\n      })\n    },\n\n    /**\n     * @description 暂停播放媒体\n     */\n    pauseMedia () {\n      for (let i = (this._videos || []).length; i--;) {\n        this._videos[i].pause()\n      }\n      // #ifdef APP-PLUS\n      const command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].pause()'\n      // #ifndef APP-PLUS-NVUE\n      let page = this.$parent\n      while (!page.$scope) page = page.$parent\n      page.$scope.$getAppWebview().evalJS(command)\n      // #endif\n      // #ifdef APP-PLUS-NVUE\n      this.$refs.web.evalJs(command)\n      // #endif\n      // #endif\n    },\n\n    /**\n     * @description 设置媒体播放速率\n     * @param {Number} rate 播放速率\n     */\n    setPlaybackRate (rate) {\n      this.playbackRate = rate\n      for (let i = (this._videos || []).length; i--;) {\n        this._videos[i].playbackRate(rate)\n      }\n      // #ifdef APP-PLUS\n      const command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].playbackRate=' + rate\n      // #ifndef APP-PLUS-NVUE\n      let page = this.$parent\n      while (!page.$scope) page = page.$parent\n      page.$scope.$getAppWebview().evalJS(command)\n      // #endif\n      // #ifdef APP-PLUS-NVUE\n      this.$refs.web.evalJs(command)\n      // #endif\n      // #endif\n    },\n\n    /**\n     * @description 设置内容\n     * @param {String} content html 内容\n     * @param {Boolean} append 是否在尾部追加\n     */\n    setContent (content, append) {\n      if (!append || !this.imgList) {\n        this.imgList = []\n      }\n      const nodes = new Parser(this).parse(content)\n      // #ifdef APP-PLUS-NVUE\n      if (this._ready) {\n        this._set(nodes, append)\n      }\n      // #endif\n      this.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes)\n\n      // #ifndef APP-PLUS-NVUE\n      this._videos = []\n      this.$nextTick(() => {\n        this._hook('onLoad')\n        this.$emit('load')\n      })\n\n      if (this.lazyLoad || this.imgList._unloadimgs < this.imgList.length / 2) {\n        // 设置懒加载，每 350ms 获取高度，不变则认为加载完毕\n        let height = 0\n        const callback = rect => {\n          if (!rect || !rect.height) rect = {}\n          // 350ms 总高度无变化就触发 ready 事件\n          if (rect.height === height) {\n            this.$emit('ready', rect)\n          } else {\n            height = rect.height\n            setTimeout(() => {\n              this.getRect().then(callback).catch(callback)\n            }, 350)\n          }\n        }\n        this.getRect().then(callback).catch(callback)\n      } else {\n        // 未设置懒加载，等待所有图片加载完毕\n        if (!this.imgList._unloadimgs) {\n          this.getRect().then(rect => {\n            this.$emit('ready', rect)\n          }).catch(() => {\n            this.$emit('ready', {})\n          })\n        }\n      }\n      // #endif\n    },\n\n    /**\n     * @description 调用插件钩子函数\n     */\n    _hook (name) {\n      for (let i = plugins.length; i--;) {\n        if (this.plugins[i][name]) {\n          this.plugins[i][name]()\n        }\n      }\n    },\n\n    // #ifdef APP-PLUS-NVUE\n    /**\n     * @description 设置内容\n     */\n    _set (nodes, append) {\n      this.$refs.web.evalJs('setContent(' + JSON.stringify(nodes).replace(/%22/g, '') + ',' + JSON.stringify([this.containerStyle.replace(/(?:margin|padding)[^;]+/g, ''), this.errorImg, this.loadingImg, this.pauseVideo, this.scrollTable, this.selectable]) + ',' + append + ')')\n    },\n\n    /**\n     * @description 接收到 web-view 消息\n     */\n    _onMessage (e) {\n      const message = e.detail.data[0]\n      switch (message.action) {\n        // web-view 初始化完毕\n        case 'onJSBridgeReady':\n          this._ready = true\n          if (this.nodes) {\n            this._set(this.nodes)\n          }\n          break\n        // 内容 dom 加载完毕\n        case 'onLoad':\n          this.height = message.height\n          this._hook('onLoad')\n          this.$emit('load')\n          break\n        // 所有图片加载完毕\n        case 'onReady':\n          this.getRect().then(res => {\n            this.$emit('ready', res)\n          }).catch(() => {\n            this.$emit('ready', {})\n          })\n          break\n        // 总高度发生变化\n        case 'onHeightChange':\n          this.height = message.height\n          break\n        // 图片点击\n        case 'onImgTap':\n          this.$emit('imgtap', message.attrs)\n          if (this.previewImg) {\n            uni.previewImage({\n              current: parseInt(message.attrs.i),\n              urls: this.imgList\n            })\n          }\n          break\n        // 链接点击\n        case 'onLinkTap': {\n          const href = message.attrs.href\n          this.$emit('linktap', message.attrs)\n          if (href) {\n            // 锚点跳转\n            if (href[0] === '#') {\n              if (this.useAnchor) {\n                dom.scrollToElement(this.$refs.web, {\n                  offset: message.offset\n                })\n              }\n            } else if (href.includes('://')) {\n              // 打开外链\n              if (this.copyLink) {\n                plus.runtime.openWeb(href)\n              }\n            } else {\n              uni.navigateTo({\n                url: href,\n                fail () {\n                  uni.switchTab({\n                    url: href\n                  })\n                }\n              })\n            }\n          }\n          break\n        }\n        case 'onPlay':\n          this.$emit('play')\n          break\n        // 获取到锚点的偏移量\n        case 'getOffset':\n          if (typeof message.offset === 'number') {\n            dom.scrollToElement(this.$refs.web, {\n              offset: message.offset + this._navigateTo.offset\n            })\n            this._navigateTo.resolve()\n          } else {\n            this._navigateTo.reject(Error('Label not found'))\n          }\n          break\n        // 点击\n        case 'onClick':\n          this.$emit('tap')\n          this.$emit('click')\n          break\n        // 出错\n        case 'onError':\n          this.$emit('error', {\n            source: message.source,\n            attrs: message.attrs\n          })\n      }\n    }\n    // #endif\n  }\n}\n</script>\n\n<style>\n/* #ifndef APP-PLUS-NVUE */\n/* 根节点样式 */\n._root {\n  padding: 1px 0;\n  overflow-x: auto;\n  overflow-y: hidden;\n  -webkit-overflow-scrolling: touch;\n}\n\n/* 长按复制 */\n._select {\n  user-select: text;\n}\n/* #endif */\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mp-html.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mp-html.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753698993466\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}